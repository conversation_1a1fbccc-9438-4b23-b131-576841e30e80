name: Analyze With Ruff

on:
  push:
    branches:
      - main
  pull_request:

permissions:
  actions: read
  contents: read
  security-events: write

jobs:
  analyze-with-ruff:
    name: Analyze With Ruff
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-python@v5
        with:
          python-version: 3.x

      - name: Install dependencies
        run: |
          python -m pip install -U pip
          pip install ruff

      - name: Run ruff
        run: ruff check
          --no-cache
          --exit-zero
          --output-format sarif > ruff-results.sarif

      - name: Upload ruff results to GitHub
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: ruff-results.sarif
          wait-for-processing: true
