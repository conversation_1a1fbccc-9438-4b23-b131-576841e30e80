name: Test Package

on:
  push:
    branches:
      - main
    paths:
      - playwright_recaptcha/**
  pull_request:
    paths:
      - playwright_recaptcha/**

permissions:
  contents: read

jobs:
  test-package:
    name: Test Package With Python ${{ matrix.python-version }}
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      max-parallel: 1
      matrix:
        python-version: ["3.9", "3.10", "3.11", "3.12", "3.13"]
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y ffmpeg
          python -m pip install -U pip
          pip install -r requirements.txt .
          playwright install --with-deps firefox

      - name: Test with pytest
        run: pytest
        env:
          CAPSOLVER_API_KEY: ${{ secrets.CAPSOLVER_API_KEY }}
