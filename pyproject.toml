[project]
name = "playwright-recaptcha-main"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "playwright>=1.54.0",
    "pydub>=0.25.1",
    "pytest-asyncio>=1.1.0",
    "setuptools>=80.9.0",
    "speechrecognition>=3.14.3",
    "tenacity>=9.1.2",
]


[tool.uv]
index-url = "https://mirrors.aliyun.com/pypi/simple/"


[tool.poe.tasks]
dev = "uv run python/main.py"
freeze =  { shell = "uv pip compile pyproject.toml -o requirements.txt" }

[dependency-groups]
dev = [
    "poethepoet>=0.36.0",
]
